@startuml

!theme spacelab
!includeurl https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

!define AWSPuml https://raw.githubusercontent.com/awslabs/aws-icons-for-plantuml/v20.0/dist
!include AWSPuml/AWSCommon.puml

' Uncomment the following line to create simplified view
' !include AWSPuml/AWSSimplified.puml

!include AWSPuml/General/Users.puml
!include AWSPuml/ManagementGovernance/CloudFormation.puml
!include AWSPuml/ApplicationIntegration/AppSync.puml
!include AWSPuml/NetworkingContentDelivery/APIGateway.puml
!include AWSPuml/SecurityIdentityCompliance/Cognito.puml
!include AWSPuml/Compute/Lambda.puml
!include AWSPuml/Database/DynamoDB.puml
!include AWSPuml/Storage/SimpleStorageService.puml
!include AWSPuml/ApplicationIntegration/StepFunctions.puml



' LAYOUT_TOP_DOWN()
LAYOUT_LEFT_RIGHT()
LAYOUT_WITH_LEGEND()

Person(btn_user, "BTN Users")
Person(mtp_user, "MTP Users")
Person(dor_user, "DOR Users")

System_Boundary(btn_boundary, "TAEx Instance - BTN") {
  Container(btn_web, "Web", "IntranetStaticWebsite \n Construct")
  AppSync(btn_api, "AppSync API", "AWS AppSync")
  Lambda(btn_lambda_1, "Lambda", "GraphQL - API")
  Lambda(btn_lambda_2, "Lambda", "preSignS3Urls")
  Lambda(btn_lambda_3, "Lambda", "StreamHandler")
  SimpleStorageService(btn_s3,"AWS S3", "Attachments Bucket BTN")
  DynamoDB(btn_db, "DynamoDB", "BTN Training Table")
  StepFunctions(btn_step, "StepFunctions", "Orchestrator BTN")
}
System_Boundary(mtp_boundary, "TAEx Instance - MTP") {
  Container(mtp_web, "Web", "IntranetStaticWebsite \n Construct")
  AppSync(mtp_api, "AppSync API", "AWS AppSync")
  Lambda(mtp_lambda_1, "Lambda", "GraphQL - API")
  Lambda(mtp_lambda_2, "Lambda", "preSignS3Urls")
  Lambda(mtp_lambda_3, "Lambda", "StreamHandler")
  SimpleStorageService(mtp_s3,"AWS S3", "Attachments Bucket MTP")
  DynamoDB(mtp_db, "DynamoDB", "MTP Training Table")
  StepFunctions(mtp_step, "StepFunctions", "Orchestrator MTP")
}
System_Boundary(dor_boundary, "TAEx Instance - DOR") {
  Container(dor_web, "Web", "IntranetStaticWebsite \n Construct")
  AppSync(dor_api, "AppSync API", "AWS AppSync")
  Lambda(dor_lambda_1, "Lambda", "GraphQL - API")
  Lambda(dor_lambda_2, "Lambda", "preSignS3Urls")
  Lambda(dor_lambda_3, "Lambda", "StreamHandler")
  SimpleStorageService(dor_s3,"AWS S3", "Attachments Bucket DOR")
  DynamoDB(dor_db, "DynamoDB", "DOR Training Table")
  StepFunctions(dor_step, "StepFunctions", "Orchestrator DOR")
}
'btn
Rel(btn_user, btn_web, "loadSinglePageApplication()")
Rel(btn_user, btn_s3, "upload/get/delete Attachments")
Rel(btn_user, btn_api, "getProject()")
Rel(btn_api, btn_lambda_1, "")
Rel(btn_api, btn_lambda_2, "")
Rel(btn_lambda_1, btn_db , "")
Rel(btn_db, btn_lambda_3, "")
Rel(btn_lambda_3, btn_step, "")
Rel(btn_step, btn_db, "")

'mtp
Rel(mtp_user, mtp_web, "loadSinglePageApplication()")
Rel(mtp_user, mtp_s3, "upload/get/delete Attachments")
Rel(mtp_user, mtp_api, "getProject()")
Rel(mtp_api, mtp_lambda_1, "")
Rel(mtp_api, mtp_lambda_2, "")
Rel(mtp_lambda_1, mtp_db , "")
Rel(mtp_db, mtp_lambda_3, "")
Rel(mtp_lambda_3, mtp_step, "")
Rel(mtp_step, mtp_db, "")

'dor
Rel(dor_user, dor_web, "loadSinglePageApplication()")
Rel(dor_user, dor_s3, "upload/get/delete Attachments")
Rel(dor_user, dor_api, "getProject()")
Rel(dor_api, dor_lambda_1, "")
Rel(dor_api, dor_lambda_2, "")
Rel(dor_lambda_1, dor_db , "")
Rel(dor_db, dor_lambda_3, "")
Rel(dor_lambda_3, dor_step, "")
Rel(dor_step, dor_db, "")

@enduml