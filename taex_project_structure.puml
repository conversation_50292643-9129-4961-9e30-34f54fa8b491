@startuml TAEX Project Structure
' !theme spacelab

package "TAEX - Turnaround Execution Management" {
    
    package "Frontend (/app)" {
        component [Angular SPA] as Angular
        component [Angular Material UI] as Material
        component [AWS Amplify] as Amplify
        component [TypeScript] as TS_Frontend
        
        Angular --> Material
        Angular --> Amplify
        Angular --> TS_Frontend
    }
    
    package "Backend (/backend)" {
        component [AWS CDK] as CDK
        component [Lambda Functions] as Lambda
        component [GraphQL Schema] as GraphQL
        component [TypeScript] as TS_Backend
        
        CDK --> Lambda
        CDK --> GraphQL
        CDK --> TS_Backend
    }
    
    package "Schema (/schema)" {
        component [GraphQL Definitions] as Schema
    }
    
    package "Documentation (/docs)" {
        component [Requirements] as Req
        component [Design] as Design
        component [API Docs] as API
        component [UAT] as UAT
    }
    
    package "AWS Infrastructure" {
        cloud "AWS Services" {
            database "DynamoDB" as DDB
            component "AppSync" as AppSync
            component "Cognito" as Cognito
            component "CloudFront" as CF
            component "S3" as S3
            component "SQS" as SQS
            component "Lambda" as AWSLambda
        }
    }
    
    package "External Systems" {
        component "Primavera P6" as P6
        component "Azure AD" as AAD
    }
    
    package "Environments" {
        node "Development" as Dev
        node "QA (taex-qa.covestro.net)" as QA
        node "Production (taex.covestro.com)" as Prod
    }
}

' Connections
Angular --> AppSync : GraphQL API
AppSync --> DDB : Data Storage
AppSync --> AWSLambda : Resolvers
Cognito --> AAD : Authentication
AWSLambda --> P6 : SOAP Integration
CF --> S3 : Static Hosting
Lambda --> SQS : Async Processing

Backend --> AWS_Infrastructure
Schema --> AppSync
Frontend --> CF

Dev --> AWS_Infrastructure
QA --> AWS_Infrastructure  
Prod --> AWS_Infrastructure

@enduml