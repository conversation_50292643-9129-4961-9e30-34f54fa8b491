@startuml
!theme spacelab
!define AWSPuml https://raw.githubusercontent.com/awslabs/aws-icons-for-plantuml/v20.0/dist
!includeurl https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

' AWS Icons
!include AWSPuml/AWSCommon.puml
!include AWSPuml/General/Users.puml
!include AWSPuml/ManagementGovernance/CloudFormation.puml
!include AWSPuml/ApplicationIntegration/AppSync.puml
!include AWSPuml/NetworkingContentDelivery/APIGateway.puml
!include AWSPuml/SecurityIdentityCompliance/Cognito.puml
!include AWSPuml/Compute/Lambda.puml
!include AWSPuml/Database/DynamoDB.puml
!include AWSPuml/Storage/SimpleStorageService.puml
!include AWSPuml/ApplicationIntegration/StepFunctions.puml

' LAYOUT_TOP_DOWN()
LAYOUT_LEFT_RIGHT()
LAYOUT_WITH_LEGEND()

' Users
Person(user, "Users", "Users from different sites (BTN, MTP, DOR, LEV, ...)")

' Multi-tenant system boundary
System_Boundary(system, "TAEx Multi-Tenant Instance") {
  Container(web, "Web", "IntranetStaticWebsite Construct", "Common interface for all tenants")
  Cognito(cognito, "Cognito", "", "")
  AppSync(api, "AppSync API", "Share to all tenants, distinguished by tenantId")
  Lambda(lambda_api, "Lambda", "GraphQL - API","Process queries with tenantId")
  Lambda(lambda_presign, "Lambda", "preSignS3URLs", "Generate URL S3 by tenant")
  Lambda(lambda_stream, "Lambda", "StreamHandler","Divided by tenant")
  DynamoDB(db, "DynamoDB", "Training Table","Partition: tenantId")
  SimpleStorageService(s3, "S3", "Attachment Bucket","Prefix by tenant")
  StepFunctions(stepfn, "StepFunctions", "Shared Workflow","State has tenant context")
}

Rel(user, web, "loadSinglePageApplication()")
Rel(user, s3, "upload/get/delete Attachments\n(by tenant)")
Rel(user, api, "performAction() [tenantId]")
Rel(web, api, "API calls [tenantId]")
Rel(api, cognito, "API calls [tenantId]")
Rel(api, lambda_api, "invoke (GraphQL, tenantId)")
Rel(api, lambda_presign, "invoke (preSignS3URLs, tenantId)")
Rel(lambda_api, db, "CRUD (tenantId)")
Rel(lambda_presign, db, "Read (tenantId)")
Rel(db, lambda_stream, "Stream events (tenantId)")
Rel(lambda_stream, stepfn, "Trigger workflow (tenantId)")
Rel(stepfn, db, "Update/Read (tenantId)")

@enduml

