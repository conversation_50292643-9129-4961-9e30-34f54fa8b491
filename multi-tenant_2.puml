@startuml
!theme spacelab
!define AWSPuml https://raw.githubusercontent.com/awslabs/aws-icons-for-plantuml/v20.0/dist
!includeurl https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

' AWS Icons
!include AWSPuml/AWSCommon.puml
!include AWSPuml/General/Users.puml
!include AWSPuml/ApplicationIntegration/AppSync.puml
!include AWSPuml/Compute/Lambda.puml
!include AWSPuml/Database/DynamoDB.puml
!include AWSPuml/Storage/SimpleStorageService.puml
!include AWSPuml/ApplicationIntegration/StepFunctions.puml

' LAYOUT_TOP_DOWN()
LAYOUT_LEFT_RIGHT()
LAYOUT_WITH_LEGEND()

Person(user, "Users", "Users from different sites (BTN, MTP, DOR, v.v.)")

System_Boundary(system, "TAEx Multi-Tenant Shared App, Isolated DB") {
  Container(web, "Web", "IntranetStaticWebsite Construct", "Common interface for all tenants")
  AppSync(api, "AppSync API", "Manage tenant context")
  Lambda(lambda_api, "Lambda", "GraphQL - API\nRoute by tenant")
  Lambda(lambda_presign, "Lambda", "preSignS3URLs\nRoute by tenant")
  Lambda(lambda_stream, "Lambda", "StreamHandler\nRoute by tenant")
  StepFunctions(stepfn, "StepFunctions", "Shared Workflow")
  SimpleStorageService(s3, "S3", "Attachment Bucket\nPrefix by tenant")
  
  ' Các database riêng biệt cho từng tenant
  DynamoDB(db_btn, "DynamoDB BTN", "BTN Training Table")
  DynamoDB(db_mtp, "DynamoDB MTP", "MTP Training Table")
  DynamoDB(db_dor, "DynamoDB DOR", "DOR Training Table")
  ' Thêm các bảng khác (LEV, v.v.) nếu cần
}

Rel(user, web, "loadSinglePageApplication()")
Rel(user, s3, "upload/get/delete Attachments\n(by tenant)")
Rel(user, api, "performAction() [tenantId]")
Rel(web, api, "API calls [tenantId]")
Rel(api, lambda_api, "invoke (GraphQL, tenantId)")
Rel(api, lambda_presign, "invoke (preSignS3URLs, tenantId)")

' Định tuyến tới bảng đúng theo tenant
Rel(lambda_api, db_btn, "CRUD (BTN tenantId)")
Rel(lambda_api, db_mtp, "CRUD (MTP tenantId)")
Rel(lambda_api, db_dor, "CRUD (DOR tenantId)")

Rel(lambda_presign, db_btn, "Read (BTN tenantId)")
Rel(lambda_presign, db_mtp, "Read (MTP tenantId)")
Rel(lambda_presign, db_dor, "Read (DOR tenantId)")

' Stream và StepFunctions cũng phân tenant
Rel(db_btn, lambda_stream, "Stream events (BTN)")
Rel(db_mtp, lambda_stream, "Stream events (MTP)")
Rel(db_dor, lambda_stream, "Stream events (DOR)")

Rel(lambda_stream, stepfn, "Trigger workflow (by tenant)")

Rel(stepfn, db_btn, "Update/Read (BTN)")
Rel(stepfn, db_mtp, "Update/Read (MTP)")
Rel(stepfn, db_dor, "Update/Read (DOR)")

@enduml